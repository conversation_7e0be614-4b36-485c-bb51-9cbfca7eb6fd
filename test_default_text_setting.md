# 测试默认初始文本设置功能

## 功能描述
在设置页中增加一个配置项，让用户可以选择是否使用默认的初始文本。当用户进入Markdown模块或文本卡片模块时，根据这个设置决定是否自动填充默认示例内容。

## 实现的功能

### 1. 配置项添加
- ✅ 在 `AppSettings` 中添加了 `useDefaultInitialText` 字段（默认为 true）
- ✅ 在 `SettingsService` 中添加了相应的更新方法
- ✅ 在设置页面中添加了开关控制项

### 2. 国际化支持
- ✅ 中文：使用默认初始文本 / 进入模块时自动填充默认示例内容
- ✅ 英文：Use Default Initial Text / Auto-fill default example content when entering modules  
- ✅ 日文：デフォルトの初期テキストを使用 / モジュール入力時にデフォルトのサンプルコンテンツを自動入力

### 3. 模块集成
- ✅ Markdown渲染控制器：根据设置决定是否使用默认Markdown内容
- ✅ 文本卡片创建器：根据设置决定是否使用默认卡片内容
- ✅ 文档拆分页面：根据设置决定是否使用默认示例文本
- ✅ Markdown分块演示：根据设置决定是否使用默认演示文本

## 测试步骤

### 测试1：默认行为（开启状态）
1. 打开应用，进入设置页面
2. 确认"使用默认初始文本"开关是开启状态（默认）
3. 进入Markdown模块，应该看到默认的示例内容
4. 进入文本卡片模块，应该看到默认的示例内容

### 测试2：关闭默认文本
1. 在设置页面关闭"使用默认初始文本"开关
2. 进入Markdown模块，应该看到空白编辑器
3. 进入文本卡片模块，应该看到空白编辑器

### 测试3：设置持久化
1. 关闭"使用默认初始文本"开关
2. 完全退出应用并重新启动
3. 确认设置仍然是关闭状态
4. 进入各个模块确认仍然是空白状态

## 技术实现细节

### 配置存储
- 配置保存在 `AppSettings` 中，通过 `SettingsService` 管理
- 使用 SharedPreferences 进行持久化存储
- 默认值为 `true`，保持向后兼容

### 模块修改
1. **MarkdownRenderController**: 在 `_validateAndCleanMarkdown` 方法中检查设置
2. **ModernCardCreator**: 在 `_setupDefaultContent` 方法中检查设置  
3. **DocumentSplitterPage**: 在 `initState` 中检查设置
4. **MarkdownBlockDemoScreen**: 在 `initState` 中检查设置

### UI组件
- 在设置页面的"内容设置"组中添加了开关控件
- 使用 `_buildSwitchItem` 方法创建开关UI
- 支持实时切换，无需重启应用

## 预期效果
- 用户可以根据个人喜好选择是否使用默认初始文本
- 对于新用户，默认开启可以提供更好的引导体验
- 对于熟练用户，可以关闭以获得干净的编辑环境
- 设置会被持久化保存，重启应用后仍然有效
