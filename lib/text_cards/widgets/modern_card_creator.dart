import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/service_locator.dart';
import '../models/enhanced_card_template.dart';
import '../utils/card_export_helper.dart';
import 'advanced_text_editor.dart';
import 'card_preview_widget.dart';
import 'export_options_dialog.dart';

/// 现代风格的卡片创建器
/// 提供世界级的用户体验和交互设计
class ModernCardCreator extends StatefulWidget {
  final Function(String content, EnhancedCardTemplate template) onCardCreated;
  final EnhancedCardTemplate? initialTemplate;
  final String? initialText;

  const ModernCardCreator({
    super.key,
    required this.onCardCreated,
    this.initialTemplate,
    this.initialText,
  });

  @override
  State<ModernCardCreator> createState() => _ModernCardCreatorState();
}

class _ModernCardCreatorState extends State<ModernCardCreator>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final TextEditingController _contentController = TextEditingController();
  late EnhancedCardTemplate _selectedTemplate;
  Map<String, dynamic> _customStyles = {};

  int _currentStep = 0; // 0: 编辑, 1: 模板, 2: 预览

  @override
  void initState() {
    super.initState();
    _initializeTemplate();
    _initializeAnimations();
    _setupDefaultContent();
  }

  void _initializeTemplate() {
    _selectedTemplate =
        widget.initialTemplate ??
        EnhancedCardTemplate.getModernTemplates().firstWhere(
          (t) => t.id == 'classic_style',
        );
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideController.forward();
    _fadeController.forward();
  }

  void _setupDefaultContent() {
    // 如果有初始文本，使用初始文本，否则根据设置决定是否使用默认内容
    if (widget.initialText != null) {
      _contentController.text = widget.initialText!;
    } else {
      // 检查设置是否启用默认初始文本
      final settingsService = ServiceLocator().settingsService;
      if (settingsService.settings.useDefaultInitialText) {
        _contentController.text = '''今日分享

在这个快节奏的时代，我们总是忙于追赶时间，却忘记了停下来感受生活的美好。

有时候，最简单的快乐就藏在日常的小事里：
• 清晨的第一缕阳光
• 咖啡的香气
• 朋友的一个微笑

让我们学会在平凡中发现不平凡，在忙碌中找到内心的宁静。

#生活感悟 #正能量 #日常分享''';
      } else {
        _contentController.text = '';
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.95,
        decoration: const BoxDecoration(
          color: Color(0xFFF8FAFC),
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: _buildCurrentView(),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFFE2E8F0),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // 标题和关闭按钮
          Row(
            children: [
              const Expanded(
                child: Text(
                  '创建精美卡片',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1E293B),
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close, color: Color(0xFF64748B)),
                style: IconButton.styleFrom(
                  backgroundColor: const Color(0xFFF1F5F9),
                  minimumSize: const Size(40, 40),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: const Color(0xFFE2E8F0),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildTabItem(0, '编辑', Icons.edit_outlined),
          _buildTabItem(1, '模板', Icons.palette_outlined),
          _buildTabItem(2, '预览', Icons.visibility_outlined),
        ],
      ),
    );
  }

  Widget _buildTabItem(int index, String title, IconData icon) {
    final isActive = _currentStep == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => _switchToStep(index),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isActive ? Colors.white : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow:
                isActive
                    ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ]
                    : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color:
                    isActive
                        ? const Color(0xFF6366F1)
                        : const Color(0xFF64748B),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  color:
                      isActive
                          ? const Color(0xFF6366F1)
                          : const Color(0xFF64748B),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentView() {
    switch (_currentStep) {
      case 0:
        return _buildEditView();
      case 1:
        return _buildTemplateView();
      case 2:
        return _buildPreviewView();
      default:
        return _buildEditView();
    }
  }

  Widget _buildEditView() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '内容编辑',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: AdvancedTextEditor(
              initialText: _contentController.text,
              template: _selectedTemplate,
              onTextChanged: (text) {
                _contentController.text = text;
              },
              onStyleChanged: (styles) {
                setState(() {
                  _customStyles = styles;
                });
              },
            ),
          ),

          const SizedBox(height: 16),

          // 编辑提示
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.tips_and_updates_outlined,
                  size: 16,
                  color: Color(0xFF6366F1),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '💡 选中文本可以修改字体、颜色和大小',
                    style: TextStyle(fontSize: 12, color: Color(0xFF6366F1)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateView() {
    final templates = EnhancedCardTemplate.getModernTemplates();

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择模板',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                final isSelected = template.id == _selectedTemplate.id;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                    HapticFeedback.selectionClick();
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: template.backgroundGradient,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.transparent,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: template.backgroundGradient.colors.first
                              .withValues(alpha: isSelected ? 0.4 : 0.2),
                          offset: const Offset(0, 8),
                          blurRadius: isSelected ? 20 : 12,
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                template.name,
                                style: TextStyle(
                                  color: template.titleColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                template.description,
                                style: TextStyle(
                                  color: template.textColor.withValues(
                                    alpha: 0.8,
                                  ),
                                  fontSize: 12,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: template.accentColor.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  template.category,
                                  style: TextStyle(
                                    color: template.accentColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Positioned(
                            top: 12,
                            right: 12,
                            child: Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewView() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '预览效果',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: Center(
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 300,
                  maxHeight: 450,
                ),
                child: CardPreviewWidget(
                  content: _contentController.text,
                  template: _selectedTemplate,
                  customStyles: _customStyles,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE2E8F0), width: 1)),
      ),
      child:
          _currentStep == 2
              ? _buildFinalStepActions()
              : _buildNormalStepActions(),
    );
  }

  Widget _buildNormalStepActions() {
    return Row(
      children: [
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: _previousStep,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('上一步'),
            ),
          ),

        if (_currentStep > 0) const SizedBox(width: 16),

        Expanded(
          flex: _currentStep > 0 ? 2 : 1,
          child: ElevatedButton(
            onPressed: _nextStepOrCreate,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6366F1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(_getActionButtonText()),
          ),
        ),
      ],
    );
  }

  Widget _buildFinalStepActions() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 第一行：上一步 + 保存到内容库
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('上一步'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _createCard,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('保存到内容库'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 第二行：导出按钮（更显眼）
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _exportCard,
            icon: const Icon(Icons.download, size: 20),
            label: const Text(
              '导出图片',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToStep(int step) {
    if (step == _currentStep) return;

    setState(() {
      _currentStep = step;
    });

    _fadeController.reset();
    _fadeController.forward();
    HapticFeedback.selectionClick();
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _switchToStep(_currentStep - 1);
    }
  }

  void _nextStepOrCreate() {
    if (_currentStep < 2) {
      _switchToStep(_currentStep + 1);
    } else {
      _createCard();
    }
  }

  void _exportCard() async {
    debugPrint('🔥 导出按钮被点击');

    try {
      HapticFeedback.lightImpact();

      // 显示导出选项对话框
      final exportConfig = await showExportOptionsDialog(context);

      // 如果用户取消了对话框，直接返回
      if (exportConfig == null) {
        debugPrint('🔥 用户取消了导出');
        return;
      }

      debugPrint(
        '🔥 用户选择了导出配置: ${exportConfig.size.label} ${exportConfig.ratio.label}',
      );

      // 显示开始导出的提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                const Text('开始导出图片...'),
              ],
            ),
            backgroundColor: Colors.blue,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      debugPrint('🔥 开始调用导出助手');

      // 使用用户选择的配置调用导出助手
      await CardExportHelper.exportCard(
        context: context,
        content: _contentController.text,
        template: _selectedTemplate,
        customStyles: _customStyles,
        exportConfig: exportConfig,
      );

      debugPrint('🔥 导出完成');

      // 导出成功时的反馈
      if (mounted) {
        // 隐藏加载提示
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                const Text('✅ 图片已成功保存到相册'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );

        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      debugPrint('🔥 导出失败: $e');

      // CardExportHelper已经显示了错误消息，这里不需要重复显示
      // 只提供触觉反馈表示失败
      if (mounted) {
        HapticFeedback.heavyImpact();
      }
    }
  }



  void _createCard() {
    final content = _contentController.text.trim();
    if (content.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入卡片内容')));
      return;
    }

    widget.onCardCreated(content, _selectedTemplate);
    Navigator.pop(context);
  }

  String _getActionButtonText() {
    switch (_currentStep) {
      case 0:
        return '选择模板';
      case 1:
        return '预览效果';
      case 2:
        return '保存到内容库';
      default:
        return '下一步';
    }
  }
}
