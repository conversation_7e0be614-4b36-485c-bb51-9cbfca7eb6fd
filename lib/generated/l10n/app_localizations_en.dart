// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'ContentPal';

  @override
  String get appNameChinese => '内容君';

  @override
  String get appDescription =>
      'Professional content processing tool that makes content creation easier';

  @override
  String get home => 'Home';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get markdown => 'Markdown';

  @override
  String get textCards => 'Text Cards';

  @override
  String get pdf => 'PDF';

  @override
  String get voice => 'Voice';

  @override
  String get html => 'HTML';

  @override
  String get svg => 'SVG';

  @override
  String get content => 'Content';

  @override
  String get trafficGuide => 'Traffic Guide';

  @override
  String get create => 'Create';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Info';

  @override
  String get search => 'Search';

  @override
  String get searchHint => 'Enter search terms...';

  @override
  String get noResults => 'No results found';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get refresh => 'Refresh';

  @override
  String get share => 'Share';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get selectAll => 'Select All';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get done => 'Done';

  @override
  String get finish => 'Finish';

  @override
  String get skip => 'Skip';

  @override
  String get continueAction => 'Continue';

  @override
  String get retry => 'Retry';

  @override
  String get reset => 'Reset';

  @override
  String get clear => 'Clear';

  @override
  String get apply => 'Apply';

  @override
  String get preview => 'Preview';

  @override
  String get download => 'Download';

  @override
  String get upload => 'Upload';

  @override
  String get file => 'File';

  @override
  String get folder => 'Folder';

  @override
  String get name => 'Name';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get size => 'Size';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get type => 'Type';

  @override
  String get status => 'Status';

  @override
  String get version => 'Version';

  @override
  String get author => 'Author';

  @override
  String get tags => 'Tags';

  @override
  String get category => 'Category';

  @override
  String get priority => 'Priority';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get public => 'Public';

  @override
  String get private => 'Private';

  @override
  String get draft => 'Draft';

  @override
  String get published => 'Published';

  @override
  String get archived => 'Archived';

  @override
  String get pdfProfessionalTool => 'PDF Professional Tool';

  @override
  String get pdfToolDescription =>
      'Powerful PDF processing capabilities that make document management easier';

  @override
  String get securityEncryption => 'Security Encryption';

  @override
  String get passwordProtectionPermissionControl =>
      'Password Protection\nPermission Control';

  @override
  String get intelligentAnnotation => 'Intelligent Annotation';

  @override
  String get highlightMarkingTextAnnotation =>
      'Highlight Marking\nText Annotation';

  @override
  String get quickSearch => 'Quick Search';

  @override
  String get fullTextSearchContentLocation =>
      'Full-text Search\nContent Location';

  @override
  String get convenientSharing => 'Convenient Sharing';

  @override
  String get multipleFormatsOneClickExport =>
      'Multiple Formats\nOne-click Export';

  @override
  String get welcomeToPdfTool => 'Welcome to PDF Professional Tool!';

  @override
  String get importFirstPdfDocument => 'Import First PDF Document';

  @override
  String get appearance => 'Appearance';

  @override
  String get followSystem => 'Follow System';

  @override
  String get languageChangeEffect =>
      'Language changes will take effect immediately';

  @override
  String get contentLibrary => 'Content Library';

  @override
  String get manageAllCards => 'Manage All Cards';

  @override
  String get templateLibrary => 'Template Library';

  @override
  String get browseBeautifulTemplates => 'Browse Beautiful Templates';

  @override
  String get inputTextToSplit => 'Input text to split';

  @override
  String get pasteOrInputLongText =>
      'Paste or input long text content, the system will intelligently recognize and split it into multiple cards';

  @override
  String get pasteClipboard => 'Paste Clipboard';

  @override
  String get clearContent => 'Clear Content';

  @override
  String cardNumber(int number) {
    return 'Card $number';
  }

  @override
  String get loadingDemoData => 'Loading demo data...';

  @override
  String get modernUIDesign =>
      '✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience';

  @override
  String editFunction(String title) {
    return 'Edit function: $title';
  }

  @override
  String deleted(String title) {
    return 'Deleted: $title';
  }

  @override
  String shareFunction(String title) {
    return 'Share function: $title';
  }

  @override
  String get createNewContent => 'Create New Content';

  @override
  String get selectContentType =>
      'Select the type of content you want to create';

  @override
  String get bold => '**Bold**';

  @override
  String get italic => '*Italic*';

  @override
  String get heading1 => '# Heading 1';

  @override
  String get heading2 => '## Heading 2';

  @override
  String get heading3 => '### Heading 3';

  @override
  String get list => '- List item\n- List item';

  @override
  String get link => '[Link text](URL)';

  @override
  String get image => '![Image description](Image URL)';

  @override
  String get code => '`Code`';

  @override
  String get codeBlock => '```\nCode block\n```';

  @override
  String get quote => '> Quote text';

  @override
  String get table =>
      '| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |';

  @override
  String get myContentLibrary => 'My Content Library';

  @override
  String get manageAndBrowseContent => 'Manage and browse all your content';

  @override
  String get contentTools => 'Content Tools';

  @override
  String get recommendedTools => 'Recommended Tools';

  @override
  String get markdownTitle => 'Markdown';

  @override
  String get markdownDescription => 'Document editing and rendering';

  @override
  String get textCardsTitle => 'Text Cards';

  @override
  String get textCardsDescription =>
      'Knowledge card customization and rendering';

  @override
  String get trafficGuideTitle => 'Traffic Guide';

  @override
  String get trafficGuideDescription => 'Traffic image and text processing';

  @override
  String get fileTools => 'File Tools';

  @override
  String get svgTitle => 'SVG';

  @override
  String get svgDescription => 'Vector graphics processing';

  @override
  String get htmlTitle => 'HTML';

  @override
  String get htmlDescription => 'Web content editing';

  @override
  String get loadingContent => 'Loading content...';

  @override
  String languageChangedTo(String language) {
    return 'Language changed to $language';
  }

  @override
  String get developer => 'Developer';

  @override
  String get contentLibraryDemo => 'Content Library Demo';

  @override
  String get viewNewContentLibraryFeatures =>
      'View new content library features';

  @override
  String get i18nDemo => 'Internationalization Demo';

  @override
  String get viewMultiLanguageSupport => 'View multi-language support effects';

  @override
  String get about => 'About';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get helpAndFeedback => 'Help & Feedback';

  @override
  String get getHelpOrProvideFeedback => 'Get help or provide feedback';

  @override
  String get helpAndFeedbackContent =>
      'If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>';

  @override
  String get selectTheme => 'Select Theme';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get systemMode => 'Follow System';

  @override
  String get personalizeYourAppExperience => 'Personalize your app experience';

  @override
  String get useDefaultInitialText => 'Use Default Initial Text';

  @override
  String get useDefaultInitialTextDescription =>
      'Auto-fill default example content when entering modules';

  @override
  String get contentSettings => 'Content Settings';
}
