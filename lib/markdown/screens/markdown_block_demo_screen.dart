import 'package:flutter/material.dart';

import '../../services/service_locator.dart';
import '../controllers/markdown_block_controller.dart';
import '../models/markdown_block.dart';
import '../models/markdown_template.dart';
import '../widgets/markdown_block_renderer.dart';
import '../widgets/markdown_block_config_panel.dart';

/// Markdown 分块渲染演示页面
class MarkdownBlockDemoScreen extends StatefulWidget {
  const MarkdownBlockDemoScreen({super.key});

  @override
  State<MarkdownBlockDemoScreen> createState() => _MarkdownBlockDemoScreenState();
}

class _MarkdownBlockDemoScreenState extends State<MarkdownBlockDemoScreen> {
  late final MarkdownBlockController _controller;
  late final TextEditingController _textController;
  
  /// 演示用的 Markdown 文本
  static const String _demoMarkdown = '''# 欢迎使用 ContentPal 分块渲染

这是一个演示 Markdown 分块渲染功能的示例文档。

## 功能特性

ContentPal 的分块渲染功能提供了以下特性：

- **自定义分隔符**：支持使用连续短横线等自定义分隔符
- **标题分隔**：可以按一级或二级标题自动分块
- **交互式操纵杆**：可拖拽的分隔杆，实时调整分块位置
- **独立模块设计**：与现有功能完全独立，便于维护

---

## 使用方法

### 1. 启用分块模式

在"分块"标签页中，开启"启用分块渲染"选项。

### 2. 配置分隔规则

您可以选择以下分隔方式：
- 按一级标题（#）分隔
- 按二级标题（##）分隔  
- 使用自定义分隔符（如 ---）

### 3. 手动调整分块

点击预览区域可以添加新的分隔杆，拖拽分隔杆可以调整分块位置。

---

## 示例内容

这里是一些示例内容，用于演示分块效果。

### 代码示例

```dart
void main() {
  runApp(MyApp());
}
```

### 列表示例

1. 第一项
2. 第二项
3. 第三项

- 无序列表项 A
- 无序列表项 B
- 无序列表项 C

---

## 总结

分块渲染功能让您可以更灵活地组织和展示 Markdown 内容，提供了更好的阅读体验。

**试试看：**
- 切换到分块模式
- 调整分隔设置
- 拖拽操纵杆重新分块
- 隐藏/显示特定分块

享受使用 ContentPal！''';

  @override
  void initState() {
    super.initState();
    _controller = MarkdownBlockController();

    // 根据设置决定是否使用默认演示文本
    final settingsService = ServiceLocator().settingsService;
    final initialText =
        settingsService.settings.useDefaultInitialText ? _demoMarkdown : '';
    _textController = TextEditingController(text: initialText);

    // 初始化分块控制器
    _controller.initialize(
      markdownText: initialText,
      config: const BlockRenderConfig(
        enabled: true,
        mode: BlockMode.auto,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Markdown 分块渲染演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetDemo,
            tooltip: '重置演示',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
            tooltip: '帮助',
          ),
        ],
      ),
      body: Row(
        children: [
          // 左侧：编辑和配置区域
          Expanded(
            flex: 1,
            child: Column(
              children: [
                // 配置面板
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    child: MarkdownBlockConfigPanel(
                      config: _controller.config,
                      onConfigChanged: _controller.updateConfig,
                    ),
                  ),
                ),
                
                // 文本编辑区域
                Expanded(
                  flex: 1,
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Markdown 编辑器',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _textController,
                            maxLines: null,
                            expands: true,
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.all(12),
                              hintText: '在这里输入 Markdown 内容...',
                            ),
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 14,
                            ),
                            onChanged: (text) {
                              _controller.updateMarkdownText(text);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 右侧：预览区域
          Expanded(
            flex: 1,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 预览标题栏
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Text(
                          'Markdown 预览',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        AnimatedBuilder(
                          animation: _controller,
                          builder: (context, child) {
                            return Text(
                              '${_controller.visibleBlocks.length} 个分块',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  
                  // 预览内容
                  Expanded(
                    child: MarkdownBlockRenderer(
                      controller: _controller,
                      template: MarkdownTemplate.getPredefinedTemplates().first,
                      selectable: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 重置演示
  void _resetDemo() {
    _textController.text = _demoMarkdown;
    _controller.initialize(
      markdownText: _demoMarkdown,
      config: const BlockRenderConfig(
        enabled: true,
        mode: BlockMode.auto,
      ),
    );
  }

  /// 显示帮助信息
  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('分块渲染帮助'),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '功能说明：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• 分块渲染可以将长文档分割成多个独立的块'),
                Text('• 每个块可以单独显示或隐藏'),
                Text('• 支持多种分隔方式：标题、自定义分隔符、手动分隔'),
                SizedBox(height: 16),
                Text(
                  '操作方法：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• 在左侧配置面板中调整分块设置'),
                Text('• 在预览区域点击可添加新的分隔杆'),
                Text('• 拖拽分隔杆可以重新调整分块位置'),
                Text('• 点击分块标题栏的眼睛图标可以隐藏/显示分块'),
                SizedBox(height: 16),
                Text(
                  '提示：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• 不同类型的分块会用不同颜色的边框区分'),
                Text('• 蓝色：一级标题分块'),
                Text('• 绿色：二级标题分块'),
                Text('• 橙色：自定义分隔符分块'),
                Text('• 灰色：手动分隔分块'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('知道了'),
            ),
          ],
        );
      },
    );
  }
}
