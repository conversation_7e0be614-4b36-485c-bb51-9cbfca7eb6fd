import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../content/content_library_demo_page.dart';
import '../demo/i18n_demo_page.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../services/service_locator.dart';
import 'language_settings_page.dart';

/// 设置页面
class SettingsPage extends StatefulWidget {
  final LocalizationService localizationService;

  const SettingsPage({super.key, required this.localizationService});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  ThemeMode _themeMode = ThemeMode.system;
  bool _useDefaultInitialText = true;

  @override
  void initState() {
    super.initState();
    _loadThemeMode();
  }

  /// 加载设置
  void _loadThemeMode() {
    if (ServiceLocator().isInitialized) {
      final settings = ServiceLocator().settingsService.settings;
      setState(() {
        _themeMode = settings.themeMode;
        _useDefaultInitialText = settings.useDefaultInitialText;
      });
    }
  }

  /// 保存主题模式
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    await ServiceLocator().settingsService.updateThemeMode(themeMode);
    setState(() {
      _themeMode = themeMode;
    });
  }

  /// 保存默认初始文本设置
  Future<void> _saveUseDefaultInitialText(bool useDefaultInitialText) async {
    await ServiceLocator().settingsService.updateUseDefaultInitialText(
      useDefaultInitialText,
    );
    setState(() {
      _useDefaultInitialText = useDefaultInitialText;
    });
  }

  /// 获取主题模式显示名称
  String _getThemeModeDisplayName(AppLocalizations l10n) {
    switch (_themeMode) {
      case ThemeMode.light:
        return l10n.lightMode;
      case ThemeMode.dark:
        return l10n.darkMode;
      case ThemeMode.system:
        return l10n.systemMode;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          l10n.settings,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // 页面标题
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.settings,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  l10n.personalizeYourAppExperience,
                  style: TextStyle(
                    fontSize: 16,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),

          // 设置选项
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                // 外观设置组
                _buildSettingsGroup(
                  title: l10n.appearance,
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.language,
                      title: l10n.language,
                      subtitle: widget.localizationService.currentLocaleName,
                      onTap: () => _openLanguageSettings(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.palette,
                      title: l10n.theme,
                      subtitle: _getThemeModeDisplayName(l10n),
                      onTap: () => _showThemeDialog(context),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 内容设置组
                _buildSettingsGroup(
                  title: l10n.contentSettings,
                  children: [
                    _buildSwitchItem(
                      context: context,
                      icon: Icons.text_snippet,
                      title: l10n.useDefaultInitialText,
                      subtitle: l10n.useDefaultInitialTextDescription,
                      value: _useDefaultInitialText,
                      onChanged: _saveUseDefaultInitialText,
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 开发者设置组
                _buildSettingsGroup(
                  title: l10n.developer,
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.library_books,
                      title: l10n.contentLibraryDemo,
                      subtitle: l10n.viewNewContentLibraryFeatures,
                      onTap: () => _openContentLibraryDemo(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.translate,
                      title: l10n.i18nDemo,
                      subtitle: l10n.viewMultiLanguageSupport,
                      onTap: () => _openI18nDemo(context),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 关于设置组
                _buildSettingsGroup(
                  title: l10n.about,
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.info_outline,
                      title: l10n.versionInfo,
                      subtitle: '1.0.0',
                      onTap: () => _showAboutDialog(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.help_outline,
                      title: l10n.helpAndFeedback,
                      subtitle: l10n.getHelpOrProvideFeedback,
                      onTap: () => _showHelpDialog(context),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置组
  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.05),
                offset: const Offset(0, 2),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingsItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: colorScheme.primary, size: 20),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: colorScheme.onSurface.withValues(alpha: 0.4),
      ),
      onTap: onTap,
    );
  }

  /// 构建开关设置项
  Widget _buildSwitchItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: colorScheme.primary, size: 20),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: colorScheme.onSurface,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: colorScheme.primary,
      ),
    );
  }

  /// 打开语言设置
  void _openLanguageSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                LanguageSettingsPage(
              localizationService: widget.localizationService,
            ),
      ),
    );
  }

  /// 打开内容库演示
  void _openContentLibraryDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ContentLibraryDemoPage()),
    );
  }

  /// 打开国际化演示
  void _openI18nDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                I18nDemoPage(localizationService: widget.localizationService),
      ),
    );
  }

  /// 显示主题选择对话框
  void _showThemeDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.selectTheme),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: Text(l10n.systemMode),
                  leading: const Icon(Icons.brightness_auto),
                  trailing:
                      _themeMode == ThemeMode.system
                          ? const Icon(
                            Icons.check,
                            color: AppTheme.primaryColor,
                          )
                          : null,
                  onTap: () {
                    _saveThemeMode(ThemeMode.system);
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(l10n.lightMode),
                  leading: const Icon(Icons.brightness_high),
                  trailing:
                      _themeMode == ThemeMode.light
                          ? const Icon(
                            Icons.check,
                            color: AppTheme.primaryColor,
                          )
                          : null,
                  onTap: () {
                    _saveThemeMode(ThemeMode.light);
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(l10n.darkMode),
                  leading: const Icon(Icons.brightness_low),
                  trailing:
                      _themeMode == ThemeMode.dark
                          ? const Icon(
                            Icons.check,
                            color: AppTheme.primaryColor,
                          )
                          : null,
                  onTap: () {
                    _saveThemeMode(ThemeMode.dark);
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'ContentPal',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(Icons.content_paste, color: Colors.white, size: 32),
      ),
      children: [const Text('专业的内容处理工具，让内容创作更轻松。')],
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.helpAndFeedback),
            content: Text(l10n.helpAndFeedbackContent),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.ok),
              ),
            ],
          ),
    );
  }
}
