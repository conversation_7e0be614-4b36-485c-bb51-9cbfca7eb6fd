{"@@locale": "en", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "ContentPal", "@appName": {"description": "The name of the application"}, "appNameChinese": "内容君", "@appNameChinese": {"description": "The Chinese name of the application"}, "appDescription": "Professional content processing tool that makes content creation easier", "@appDescription": {"description": "Description of the application"}, "home": "Home", "@home": {"description": "Home page title"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "language": "Language", "@language": {"description": "Language setting label"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "lightTheme": "Light", "@lightTheme": {"description": "Light theme option"}, "darkTheme": "Dark", "@darkTheme": {"description": "Dark theme option"}, "systemTheme": "System", "@systemTheme": {"description": "System theme option"}, "markdown": "<PERSON><PERSON>", "@markdown": {"description": "Markdown module name"}, "textCards": "Text Cards", "@textCards": {"description": "Text Cards module name"}, "pdf": "PDF", "@pdf": {"description": "PDF module name"}, "voice": "Voice", "@voice": {"description": "Voice module name"}, "html": "HTML", "@html": {"description": "HTML module name"}, "svg": "SVG", "@svg": {"description": "SVG module name"}, "content": "Content", "@content": {"description": "Content module name"}, "trafficGuide": "Traffic Guide", "@trafficGuide": {"description": "Traffic Guide module name"}, "create": "Create", "@create": {"description": "Create button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message prefix"}, "success": "Success", "@success": {"description": "Success message prefix"}, "warning": "Warning", "@warning": {"description": "Warning message prefix"}, "info": "Info", "@info": {"description": "Info message prefix"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "searchHint": "Enter search terms...", "@searchHint": {"description": "Search input hint text"}, "noResults": "No results found", "@noResults": {"description": "No search results message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "share": "Share", "@share": {"description": "Share button text"}, "export": "Export", "@export": {"description": "Export button text"}, "import": "Import", "@import": {"description": "Import button text"}, "copy": "Copy", "@copy": {"description": "Copy button text"}, "paste": "Paste", "@paste": {"description": "Paste button text"}, "cut": "Cut", "@cut": {"description": "Cut button text"}, "undo": "Undo", "@undo": {"description": "Undo button text"}, "redo": "Redo", "@redo": {"description": "Redo button text"}, "selectAll": "Select All", "@selectAll": {"description": "Select all button text"}, "close": "Close", "@close": {"description": "Close button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "done": "Done", "@done": {"description": "Done button text"}, "finish": "Finish", "@finish": {"description": "Finish button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "continueAction": "Continue", "@continueAction": {"description": "Continue button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "reset": "Reset", "@reset": {"description": "Reset button text"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "preview": "Preview", "@preview": {"description": "Preview button text"}, "download": "Download", "@download": {"description": "Download button text"}, "upload": "Upload", "@upload": {"description": "Upload button text"}, "file": "File", "@file": {"description": "File label"}, "folder": "Folder", "@folder": {"description": "Folder label"}, "name": "Name", "@name": {"description": "Name field label"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "size": "Size", "@size": {"description": "Size field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "type": "Type", "@type": {"description": "Type field label"}, "status": "Status", "@status": {"description": "Status field label"}, "version": "Version", "@version": {"description": "Version field label"}, "author": "Author", "@author": {"description": "Author field label"}, "tags": "Tags", "@tags": {"description": "Tags field label"}, "category": "Category", "@category": {"description": "Category field label"}, "priority": "Priority", "@priority": {"description": "Priority field label"}, "high": "High", "@high": {"description": "High priority"}, "medium": "Medium", "@medium": {"description": "Medium priority"}, "low": "Low", "@low": {"description": "Low priority"}, "enabled": "Enabled", "@enabled": {"description": "Enabled status"}, "disabled": "Disabled", "@disabled": {"description": "Disabled status"}, "online": "Online", "@online": {"description": "Online status"}, "offline": "Offline", "@offline": {"description": "Offline status"}, "connected": "Connected", "@connected": {"description": "Connected status"}, "disconnected": "Disconnected", "@disconnected": {"description": "Disconnected status"}, "available": "Available", "@available": {"description": "Available status"}, "unavailable": "Unavailable", "@unavailable": {"description": "Unavailable status"}, "active": "Active", "@active": {"description": "Active status"}, "inactive": "Inactive", "@inactive": {"description": "Inactive status"}, "public": "Public", "@public": {"description": "Public visibility"}, "private": "Private", "@private": {"description": "Private visibility"}, "draft": "Draft", "@draft": {"description": "Draft status"}, "published": "Published", "@published": {"description": "Published status"}, "archived": "Archived", "@archived": {"description": "Archived status"}, "pdfProfessionalTool": "PDF Professional Tool", "@pdfProfessionalTool": {"description": "PDF module title"}, "pdfToolDescription": "Powerful PDF processing capabilities that make document management easier", "@pdfToolDescription": {"description": "PDF module description"}, "securityEncryption": "Security Encryption", "@securityEncryption": {"description": "PDF security feature title"}, "passwordProtectionPermissionControl": "Password Protection\nPermission Control", "@passwordProtectionPermissionControl": {"description": "PDF security feature description"}, "intelligentAnnotation": "Intelligent Annotation", "@intelligentAnnotation": {"description": "PDF annotation feature title"}, "highlightMarkingTextAnnotation": "Highlight Marking\nText Annotation", "@highlightMarkingTextAnnotation": {"description": "PDF annotation feature description"}, "quickSearch": "Quick Search", "@quickSearch": {"description": "PDF search feature title"}, "fullTextSearchContentLocation": "Full-text Search\nContent Location", "@fullTextSearchContentLocation": {"description": "PDF search feature description"}, "convenientSharing": "Convenient Sharing", "@convenientSharing": {"description": "PDF sharing feature title"}, "multipleFormatsOneClickExport": "Multiple Formats\nOne-click Export", "@multipleFormatsOneClickExport": {"description": "PDF sharing feature description"}, "welcomeToPdfTool": "Welcome to PDF Professional Tool!", "@welcomeToPdfTool": {"description": "Welcome message for PDF tool"}, "importFirstPdfDocument": "Import First PDF Document", "@importFirstPdfDocument": {"description": "Import PDF button text"}, "appearance": "Appearance", "@appearance": {"description": "Appearance settings section title"}, "followSystem": "Follow System", "@followSystem": {"description": "Follow system theme option"}, "languageChangeEffect": "Language changes will take effect immediately", "@languageChangeEffect": {"description": "Language change notification message"}, "contentLibrary": "Content Library", "@contentLibrary": {"description": "Content library title"}, "manageAllCards": "Manage All Cards", "@manageAllCards": {"description": "Content library subtitle"}, "templateLibrary": "Template Library", "@templateLibrary": {"description": "Template library title"}, "browseBeautifulTemplates": "Browse Beautiful Templates", "@browseBeautifulTemplates": {"description": "Template library subtitle"}, "inputTextToSplit": "Input text to split", "@inputTextToSplit": {"description": "Text splitter input title"}, "pasteOrInputLongText": "Paste or input long text content, the system will intelligently recognize and split it into multiple cards", "@pasteOrInputLongText": {"description": "Text splitter input description"}, "pasteClipboard": "Paste Clipboard", "@pasteClipboard": {"description": "Paste from clipboard button"}, "clearContent": "Clear Content", "@clearContent": {"description": "Clear content button"}, "cardNumber": "Card {number}", "@cardNumber": {"description": "Default card title with number", "placeholders": {"number": {"type": "int", "description": "Card number"}}}, "loadingDemoData": "Loading demo data...", "@loadingDemoData": {"description": "Loading demo data message"}, "modernUIDesign": "✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience", "@modernUIDesign": {"description": "Feature description for content library"}, "editFunction": "Edit function: {title}", "@editFunction": {"description": "Edit function message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "deleted": "Deleted: {title}", "@deleted": {"description": "Deleted item message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "shareFunction": "Share function: {title}", "@shareFunction": {"description": "Share function message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "createNewContent": "Create New Content", "@createNewContent": {"description": "Create new content title"}, "selectContentType": "Select the type of content you want to create", "@selectContentType": {"description": "Create content type selection description"}, "bold": "**Bold**", "@bold": {"description": "Bold markdown syntax"}, "italic": "*Italic*", "@italic": {"description": "Italic markdown syntax"}, "heading1": "# Heading 1", "@heading1": {"description": "Heading 1 markdown syntax"}, "heading2": "## Heading 2", "@heading2": {"description": "Heading 2 markdown syntax"}, "heading3": "### Heading 3", "@heading3": {"description": "Heading 3 markdown syntax"}, "list": "- List item\n- List item", "@list": {"description": "List markdown syntax"}, "link": "[Link text](URL)", "@link": {"description": "Link markdown syntax"}, "image": "![Image description](Image URL)", "@image": {"description": "Image markdown syntax"}, "code": "`Code`", "@code": {"description": "Inline code markdown syntax"}, "codeBlock": "```\nCode block\n```", "@codeBlock": {"description": "Code block markdown syntax"}, "quote": "> Quote text", "@quote": {"description": "Quote markdown syntax"}, "table": "| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |", "@table": {"description": "Table markdown syntax"}, "myContentLibrary": "My Content Library", "@myContentLibrary": {"description": "My content library title"}, "manageAndBrowseContent": "Manage and browse all your content", "@manageAndBrowseContent": {"description": "Content library description"}, "contentTools": "Content Tools", "@contentTools": {"description": "Content tools section title"}, "recommendedTools": "Recommended Tools", "@recommendedTools": {"description": "Recommended tools section title"}, "markdownTitle": "<PERSON><PERSON>", "@markdownTitle": {"description": "Markdown tool title"}, "markdownDescription": "Document editing and rendering", "@markdownDescription": {"description": "Markdown tool description"}, "textCardsTitle": "Text Cards", "@textCardsTitle": {"description": "Text cards tool title"}, "textCardsDescription": "Knowledge card customization and rendering", "@textCardsDescription": {"description": "Text cards tool description"}, "trafficGuideTitle": "Traffic Guide", "@trafficGuideTitle": {"description": "Traffic guide tool title"}, "trafficGuideDescription": "Traffic image and text processing", "@trafficGuideDescription": {"description": "Traffic guide tool description"}, "fileTools": "File Tools", "@fileTools": {"description": "File tools section title"}, "svgTitle": "SVG", "@svgTitle": {"description": "SVG tool title"}, "svgDescription": "Vector graphics processing", "@svgDescription": {"description": "SVG tool description"}, "htmlTitle": "HTML", "@htmlTitle": {"description": "HTML tool title"}, "htmlDescription": "Web content editing", "@htmlDescription": {"description": "HTML tool description"}, "loadingContent": "Loading content...", "@loadingContent": {"description": "Loading content message"}, "languageChangedTo": "Language changed to {language}", "@languageChangedTo": {"description": "Language change success message", "placeholders": {"language": {"type": "String", "description": "Language name"}}}, "developer": "Developer", "@developer": {"description": "Developer settings section title"}, "contentLibraryDemo": "Content Library Demo", "@contentLibraryDemo": {"description": "Content library demo title"}, "viewNewContentLibraryFeatures": "View new content library features", "@viewNewContentLibraryFeatures": {"description": "Content library demo description"}, "i18nDemo": "Internationalization Demo", "@i18nDemo": {"description": "I18n demo title"}, "viewMultiLanguageSupport": "View multi-language support effects", "@viewMultiLanguageSupport": {"description": "I18n demo description"}, "about": "About", "@about": {"description": "About settings section title"}, "versionInfo": "Version Info", "@versionInfo": {"description": "Version info title"}, "helpAndFeedback": "Help & Feedback", "@helpAndFeedback": {"description": "Help and feedback title"}, "getHelpOrProvideFeedback": "Get help or provide feedback", "@getHelpOrProvideFeedback": {"description": "Help and feedback description"}, "helpAndFeedbackContent": "If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>", "@helpAndFeedbackContent": {"description": "Help and feedback dialog content"}, "selectTheme": "Select Theme", "@selectTheme": {"description": "Select theme dialog title"}, "lightMode": "Light Mode", "@lightMode": {"description": "Light theme mode"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark theme mode"}, "systemMode": "Follow System", "@systemMode": {"description": "System theme mode"}, "personalizeYourAppExperience": "Personalize your app experience", "@personalizeYourAppExperience": {"description": "Settings page subtitle"}, "useDefaultInitialText": "Use Default Initial Text", "@useDefaultInitialText": {"description": "Setting to use default initial text"}, "useDefaultInitialTextDescription": "Auto-fill default example content when entering modules", "@useDefaultInitialTextDescription": {"description": "Description for default initial text setting"}, "contentSettings": "Content Settings", "@contentSettings": {"description": "Content settings section title"}}